<template>
  <div v-if="showDownloadGuide" @click="handleDownload" class="download-guide-container">
    <div class="download-guide-content">
      <div v-if="canClose" class="close-btn" @click.stop="handleClose">
        <ZIcon type="icon-guanbi1" color="#fff" :size="14" />
      </div>
      <div class="app-info">
        <img
          :src="downloadConfig.icon ? getServerSideImageUrl(downloadConfig.icon) : defaultAppIcon"
          alt="App Icon"
          class="app-icon"
        />
        <div class="app-details">
          <div v-if="downloadConfig.slogan" class="app-slogan">
            {{ downloadConfig.slogan }}
          </div>
        </div>
      </div>
      <AutoResizeText
        :text="downloadConfig.button_text || 'Download'"
        :container-width="92"
        :container-height="36"
        :padding="10"
        :max-font-size="16"
        :min-font-size="10"
        :auto-line-height="true"
        container-class="download-btn"
      />
    </div>
  </div>
  <CopyLinkTip v-model="showDialog" :copy-url="currentUrl" />
</template>

<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import { storeToRefs } from "pinia";
import { CHANEL_TYPE } from "@/utils/config/GlobalConstant";
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";
import { useGlobalStore } from "@/stores";
import { MobileWindowManager } from "@/utils/managers/MobileWindowManager";
import { getServerSideImageUrl } from "@/utils/core/tools";
import CopyLinkTip from "@/components/ZPopDialog/CopyLinkTip.vue";

// 默认应用图标 - 使用favicon
const defaultAppIcon = "/favicon.ico";

// Store引用
const autoPopMgrStore = useAutoPopMgrStore();
const { downloadGuideConfig } = storeToRefs(autoPopMgrStore);
const globalStore = useGlobalStore();
// 本地状态
const showDownloadGuide = ref(false);
// CopyLinkTip 响应式数据
const showDialog = ref(false);
const currentUrl = ref("");
// 计算属性：下载配置
const downloadConfig = computed(() => {
  return downloadGuideConfig.value || {};
});

// 计算属性：是否可以关闭
const canClose = computed(() => {
  // close_type: 1-允许关闭, 2-不允许关闭
  return downloadConfig.value?.close_type !== 2;
});

// 计算属性：检查是否应该显示下载引导
const shouldShowDownloadGuide = computed(() => {
  return !!downloadGuideConfig.value?.download_url;
});

// 处理关闭
const handleClose = () => {
  // 只有在允许关闭时才能关闭
  if (canClose.value) {
    showDownloadGuide.value = false;
  }
};

// 处理下载
const handleDownload = () => {
  if (globalStore.channel === CHANEL_TYPE.MAYA) {
    // 显示copylink弹窗
    currentUrl.value = downloadConfig.value?.download_url ?? "";
    showDialog.value = true;
    return;
  }
  const downloadUrl = downloadConfig.value?.download_url;

  if (downloadUrl) {
    window.open(downloadUrl, "_blank");
  }
};

// 监听是否应该显示下载引导的变化
watch(
  shouldShowDownloadGuide,
  (shouldShow) => {
    // 只在需要显示且当前未显示时才设置为 true
    if (shouldShow && !showDownloadGuide.value) {
      showDownloadGuide.value = true;
    }
  },
  { immediate: true } // 立即执行一次
);

// 暴露方法供外部调用
defineExpose({
  show: () => {
    showDownloadGuide.value = true;
  },
  hide: () => {
    showDownloadGuide.value = false;
  },
});
</script>

<style lang="scss" scoped>
.download-guide-container {
  position: fixed;
  bottom: 80px; /* 避免遮挡底部导航栏，TabBar高度54px + 安全区域 */
  left: 12px;
  right: 12px;
  z-index: 999; /* 低于弹窗组件的z-index */
  background: #ac1140;
  padding: 8px 12px;
  border-radius: 12px; /* 添加圆角 */
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  animation: slideUp 0.3s ease-out;
  font-family: "Inter";
  width: 94vw;
  cursor: pointer;
}

.download-guide-content {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
}

.close-btn {
  position: absolute;
  top: -13px;
  right: -18px;
  width: 20px;
  height: 20px;
  font-weight: 800;
  line-height: 20px;
  text-align: center;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 20px;
}

.app-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.app-icon {
  width: 36px;
  height: 36px;
  border-radius: 12px;
  object-fit: cover;
}

.app-details {
  flex: 1;
  color: white;
  font-family: "Inter";
  font-weight: 500;
  font-size: 12px;
  line-height: 18px;
  letter-spacing: 0%;
}

.app-title {
  font-size: 14px;
  font-weight: 700;
  margin-bottom: 2px;
}

.app-description {
  font-size: 12px;
  font-weight: 500;
  line-height: 1.3;
}

.app-slogan {
  margin-top: 2px;
  text-align: left;
  color: white;
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
  /* 最多显示两行，超出文本溢出 */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
}

.download-btn {
  background: #fff;
  color: #ac1140;
  width: 92px;
  height: 36px;
  border-radius: 100px;
  font-weight: 700;
  padding: 0;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>
