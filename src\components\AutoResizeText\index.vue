<template>
  <div
    class="auto-resize-text"
    ref="containerRef"
    :class="containerClass"
    :style="containerStyle"
    @click="$emit('click', $event)"
  >
    <span ref="textRef" :style="textStyle">
      <slot>{{ text }}</slot>
    </span>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, nextTick, onMounted } from "vue";

interface Props {
  text?: string;
  maxFontSize?: number;
  minFontSize?: number;
  containerWidth?: number;
  containerHeight?: number;
  padding?: number;
  fontWeight?: string | number;
  fontFamily?: string;
  containerClass?: string;
  containerStyle?: Record<string, any>;
  disabled?: boolean;
  multiLine?: boolean;
  maxLines?: number;
  lineHeight?: number;
  autoLineHeight?: boolean; // 是否自动调整行高
  minLineHeight?: number; // 最小行高
  maxLineHeight?: number; // 最大行高
}

const props = withDefaults(defineProps<Props>(), {
  text: "",
  maxFontSize: 14,
  minFontSize: 10,
  containerWidth: 92,
  containerHeight: 36,
  padding: 10,
  fontWeight: "600",
  fontFamily: "Inter",
  containerClass: "",
  containerStyle: () => ({}),
  disabled: false,
  multiLine: true,
  maxLines: 2,
  lineHeight: 1,
  autoLineHeight: false,
  minLineHeight: 0.8,
  maxLineHeight: 2,
});

const emit = defineEmits<{
  click: [event: MouseEvent];
  fontSizeChanged: [fontSize: number];
}>();

const containerRef = ref<HTMLElement | null>(null);
const textRef = ref<HTMLElement | null>(null);
const currentFontSize = ref(props.maxFontSize);

// 计算可用宽度和高度（减去padding）
const availableWidth = computed(() => {
  return props.containerWidth - props.padding * 2;
});

const availableHeight = computed(() => {
  return props.containerHeight - props.padding * 2;
});

// 计算动态行高
const calculateLineHeight = (fontSize: number, availableHeight: number): number => {
  if (!props.autoLineHeight || !props.multiLine) {
    return props.lineHeight;
  }

  // 基于字体大小和可用高度计算最优行高
  const estimatedLineHeight = availableHeight / (fontSize * props.maxLines);

  // 限制在最小和最大行高范围内
  return Math.max(props.minLineHeight, Math.min(props.maxLineHeight, estimatedLineHeight));
};

// 文本样式
const textStyle = computed(() => {
  const baseStyle = {
    fontSize: currentFontSize.value + "px",
    fontWeight: props.fontWeight,
    fontFamily: props.fontFamily,
    lineHeight: props.lineHeight,
    transition: "font-size 0.2s ease, line-height 0.2s ease",
    whiteSpace: props.multiLine ? "normal" : "nowrap",
    wordBreak: props.multiLine ? "break-word" : "normal",
    overflow: "hidden",
  };

  // 多行文本样式
  if (props.multiLine && props.maxLines > 0) {
    return {
      ...baseStyle,
      display: "-webkit-box",
      WebkitLineClamp: props.maxLines,
      WebkitBoxOrient: "vertical",
      // 确保多行文本在容器中居中
      alignSelf: "center",
      justifySelf: "center",
    };
  }

  // 单行文本样式
  return {
    ...baseStyle,
    // 单行文本居中
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    height: "100%",
  };
});

// 自适应字体大小函数
const adjustFontSize = () => {
  if (!containerRef.value || props.disabled) return;

  const text = props.text || containerRef.value.textContent || "";
  if (!text.trim()) return;

  let fontSize = props.maxFontSize;
  const minFontSize = props.minFontSize;
  const maxWidth = availableWidth.value;
  const maxHeight = availableHeight.value;

  // 创建临时元素来测量文本尺寸
  const tempElement = document.createElement("div");
  tempElement.style.position = "absolute";
  tempElement.style.visibility = "hidden";
  tempElement.style.width = maxWidth + "px";
  tempElement.style.fontWeight = String(props.fontWeight);
  tempElement.style.fontFamily = props.fontFamily;
  tempElement.style.wordBreak = props.multiLine ? "break-word" : "normal";
  tempElement.style.whiteSpace = props.multiLine ? "normal" : "nowrap";

  // 如果是多行模式，设置行数限制
  if (props.multiLine && props.maxLines > 0) {
    tempElement.style.display = "-webkit-box";
    tempElement.style.webkitLineClamp = String(props.maxLines);
    tempElement.style.webkitBoxOrient = "vertical";
    tempElement.style.overflow = "hidden";
  }

  tempElement.textContent = text;
  document.body.appendChild(tempElement);

  try {
    let bestFontSize = minFontSize;
    let bestLineHeight = props.lineHeight;

    // 逐步减小字体大小直到文本适合容器
    while (fontSize >= minFontSize) {
      // 计算当前字体大小对应的行高
      const lineHeight = calculateLineHeight(fontSize, maxHeight);

      tempElement.style.fontSize = fontSize + "px";
      tempElement.style.lineHeight = String(lineHeight);

      const textWidth = tempElement.offsetWidth;
      const textHeight = tempElement.offsetHeight;

      // 检查是否适合容器
      const fitsWidth = props.multiLine || textWidth <= maxWidth;
      const fitsHeight = !props.multiLine || textHeight <= maxHeight;

      if (fitsWidth && fitsHeight) {
        bestFontSize = fontSize;
        bestLineHeight = lineHeight;
        break;
      }
      fontSize -= 0.5;
    }

    // 应用计算出的字体大小和行高
    const finalSize = Math.max(bestFontSize, minFontSize);
    currentFontSize.value = finalSize;

    // 触发字体大小变化事件
    emit("fontSizeChanged", finalSize);

    console.log(
      `AutoResizeText - Text: "${text}", Font size: ${finalSize}px, Line height: ${bestLineHeight}, Mode: ${
        props.multiLine ? "multiline" : "single"
      }`
    );
  } finally {
    // 清理临时元素
    document.body.removeChild(tempElement);
  }
};

// 监听文本变化
watch(
  () => props.text,
  () => {
    nextTick(() => {
      adjustFontSize();
    });
  }
);

// 监听容器尺寸和多行配置变化
watch(
  [
    () => props.containerWidth,
    () => props.containerHeight,
    () => props.padding,
    () => props.multiLine,
    () => props.maxLines,
    () => props.lineHeight,
    () => props.autoLineHeight,
    () => props.minLineHeight,
    () => props.maxLineHeight,
  ],
  () => {
    nextTick(() => {
      adjustFontSize();
    });
  }
);

// 组件挂载后初始化
onMounted(() => {
  nextTick(() => {
    adjustFontSize();
  });
});

// 暴露方法供外部调用
defineExpose({
  adjustFontSize,
  getCurrentFontSize: () => currentFontSize.value,
});
</script>

<style lang="scss" scoped>
.auto-resize-text {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  span {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;

    /* 确保文本内容居中显示 */
    &[style*="-webkit-box"] {
      /* 多行文本使用 webkit-box 时的居中处理 */
      display: -webkit-box !important;
      -webkit-box-align: center;
      -webkit-box-pack: center;
    }
  }
}
</style>
